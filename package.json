{"name": "data_anotator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@mui/x-charts": "^7.24.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.1.7", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.6.1", "@shadcn/ui": "^0.0.4", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.20.6", "@types/chart.js": "^2.9.41", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer": "^2.4.1", "framer-motion": "^10.18.0", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.473.0", "motion": "^12.6.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.0", "react-select": "^5.10.1", "react-timezone-select": "^3.2.8", "react-to-pdf": "^2.0.1", "react-toastify": "^11.0.5", "recharts": "^2.15.0", "shadcn-ui": "^0.9.4", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tanstack/eslint-plugin-query": "^5.73.3", "@types/jwt-decode": "^2.2.1", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-google-recaptcha": "^2.1.9", "@types/react-pdf": "^6.2.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}