Stack trace:
Frame         Function      Args
0007FFFF8E70  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF8E70, 0007FFFF7D70) msys-2.0.dll+0x1FEBA
0007FFFF8E70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9148) msys-2.0.dll+0x67F9
0007FFFF8E70  000210046832 (000210285FF9, 0007FFFF8D28, 0007FFFF8E70, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E70  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E70  0002100690B4 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9150  00021006A49D (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA7EA00000 ntdll.dll
7FFA7C810000 KERNEL32.DLL
7FFA7BF70000 KERNELBASE.dll
7FFA7C970000 USER32.dll
7FFA7C370000 win32u.dll
7FFA7CB50000 GDI32.dll
7FFA7C520000 gdi32full.dll
7FFA7BB30000 msvcp_win.dll
7FFA7BD60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA7DEA0000 advapi32.dll
7FFA7E8E0000 msvcrt.dll
7FFA7E6B0000 sechost.dll
7FFA7CD50000 RPCRT4.dll
7FFA7B120000 CRYPTBASE.DLL
7FFA7C6F0000 bcryptPrimitives.dll
7FFA7CC60000 IMM32.DLL
