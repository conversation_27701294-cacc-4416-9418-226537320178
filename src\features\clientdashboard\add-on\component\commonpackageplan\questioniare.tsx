import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import CustomToast from "@/_components/common/customtoast";
import TimezoneSelect, { allTimezones, ITimezone } from "react-timezone-select";
import { getAllPackages } from "@/features/admindashboard/packageadmin/component/package/api_package/api_package";

// Import shadcn components
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { BackButton } from "@/_components/common";
import PaymentModal from "./payment.selection";
import { format, addDays, addHours, parse } from "date-fns";

// Form schema
const formSchema = z.object({
  packageCategory: z
    .string()
    .min(1, { message: "Package category is required" }),
  timeZone: z.object({
    value: z.string().min(1, "Time zone is required"),
    label: z.string().optional(),
    offset: z.number().optional(),
    abbrev: z.string().optional(),
    altName: z.string().optional(),
  }),
  industry: z.string().min(1, { message: "Industry is required" }),
  annotationCategory: z
    .string()
    .min(1, { message: "Annotation category is required" }),
  fromTime: z.string().min(1, { message: "From time is required" }),
  toTime: z.string().min(1, { message: "To time is required" }),
  description: z.string().optional(),
  paymentMethod: z.enum(["dodo", "paypal", "bank"], {
    required_error: "Please select a payment method",
  }),
  startOn: z.string().optional().optional().nullable(),
});

export type FormValues = z.infer<typeof formSchema>;

const Questioniare = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showToast, setShowToast] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [formValid, setFormValid] = useState(false);

  const [activePackages, setActivePackages] = useState<
    { id: string; name: string }[]
  >([]);
   console.log("isloading ad active pacages questine", activePackages);
  const [isLoadingPackages, setIsLoadingPackages] = useState(true);
  if (isLoadingPackages) {
    return <div>Loading packages...</div>;
  }
  // Calculate default start date (3 days from now)
  const today = new Date();
  const minSelectableDate = format(addDays(today, 3), "yyyy-MM-dd");
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + 3);
  const defaultDate = futureDate.toISOString().split("T")[0];

  // Get packageId and packageName from URL query params
  const searchParams = new URLSearchParams(location.search);
  const packageId = searchParams.get("packageId") || "";
  const packageName = searchParams.get("packageName") || "";

  // Form setup
  const {
    register,
    handleSubmit,
    setValue,
    control,
    watch,
    formState: { errors, isValid },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      packageCategory: "",
      timeZone: undefined,
      industry: "",
      annotationCategory: "",
      fromTime: "",
      toTime: "",
      description: "",
      paymentMethod: "dodo",
      startOn: defaultDate,
    },
    mode: "onChange",
  });

  // Watch all form fields to check validity
  const formValues = watch();
  useEffect(() => {
    setFormValid(isValid);
  }, [formValues, isValid]);

  // Handle select change
  const handleSelectChange = (field: keyof FormValues, value: string) => {
    setValue(field, value, { shouldValidate: true });
  };

  // Fetch packages from API
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setIsLoadingPackages(true);
        const response = await getAllPackages();

        if (
          response &&
          response.data &&
          Array.isArray(response.data.packages)
        ) {
          // Filter only active packages
          const active = response.data.packages
            .filter((pkg: any) => pkg.isActive)
            .map((pkg: any) => ({ id: pkg.id, name: pkg.name }));
          setActivePackages(active);

          if (packageId) {
            setValue("packageCategory", packageId, { shouldValidate: true });
          }
          else if (active.length > 0) {
            setValue("packageCategory", active[0].id, { shouldValidate: true });
          }
        }
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setIsLoadingPackages(false);
      }
    };

    fetchPackages();
  }, [location.search, packageId, setValue]);

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      setIsSubmitting(true);
      setErrorMessage(null);

      const apiData = {
        packageId: packageId || data.packageCategory,
        availableFrom: data.fromTime,
        availableTo: data.toTime,
        timezone: data.timeZone.value,
        industry: data.industry,
        category: data.annotationCategory,
        description: data.description,
        paymentMethod: data.paymentMethod,
        startOn: data.startOn,
      };

      console.log("Submitting data to API using PATCH method:", apiData);
      navigate("/auth/address-billing", {
        state: { formData: apiData },
      });
    } catch (error: any) {
      console.error("Error submitting form:", error);
      const errorMsg =
        error.response?.data?.message ||
        error.message ||
        "Failed to submit form. Please try again.";
      setErrorMessage(errorMsg);
      setShowToast(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Automatically set toTime to 9 hours after fromTime when fromTime changes
  useEffect(() => {
    const fromTime = watch("fromTime");
    if (fromTime) {
      const parsedTime = parse(fromTime, "HH:mm", new Date());
      const toTimeDate = addHours(parsedTime, 9);
      const formattedToTime = format(toTimeDate, "HH:mm");
      setValue("toTime", formattedToTime, { shouldValidate: true });
    }
  }, [watch("fromTime"), setValue]);

  return (
    <div className="w-[70%] mx-auto bg-white rounded-lg p-6 lg:p-1 xl:p-2 2xl:p-20">
      <div className="flex gap-3 mb-6">
        <BackButton />
        <h1 className="text-2xl lg:text-3xl font-semibold text-gray-800 ">
          Questionnaire
        </h1>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Package Category */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Package Category
          </label>
          <div className="w-full border-gradient bg-[#F9EFEF] rounded-md p-2">
            <input
              type="text"
              value={packageName || "No package selected"}
              readOnly
              className="w-full bg-transparent outline-none text-gray-800"
            />
          </div>
          {errors.packageCategory && (
            <p className="text-red-500 text-xs mt-1">
              {errors.packageCategory.message}
            </p>
          )}
        </div>

        {/* Time Zone - Improved Styling */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Time Zone</label>
          <Controller
            name="timeZone"
            control={control}
            render={({ field }) => (
              <div className="relative">
                <TimezoneSelect
                  value={field.value as ITimezone}
                  onChange={field.onChange}
                  timezones={allTimezones}
                  className="w-full"
                  styles={{
                    control: (base) => ({
                      ...base,
                      borderColor: "#FF577F",
                      minHeight: "42px",
                      backgroundColor: "#F9EFEF",
                      borderRadius: "0.375rem",
                      "&:hover": {
                        borderColor: "#FF577F",
                      },
                    }),
                    menu: (base) => ({
                      ...base,
                      zIndex: 9999,
                      maxHeight: "300px",
                      overflowY: "auto",
                      backgroundColor: "#F9EFEF",
                      border: "1px solid #FF577F",
                      borderRadius: "0.375rem",
                      marginTop: "4px",
                      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                    }),
                    option: (base, { isFocused, isSelected }) => ({
                      ...base,
                      backgroundColor: isSelected
                        ? "#FF577F"
                        : isFocused
                          ? "#FFD6E0"
                          : "#F9EFEF",
                      color: isSelected ? "white" : "black",
                      "&:active": {
                        backgroundColor: "#FF577F",
                        color: "white",
                      },
                    }),
                  }}
                />
              </div>
            )}
          />
          {errors.timeZone && (
            <p className="text-red-500 text-xs mt-1">Time zone is required</p>
          )}
        </div>

        {/* Industry - Improved Styling */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Industry</label>
          <Select
            onValueChange={(value) => handleSelectChange("industry", value)}
          >
            <SelectTrigger className="w-full border-gradient bg-[#F9EFEF] hover:bg-[#F9EFEF] focus:ring-2 focus:ring-[#FF577F]">
              <SelectValue placeholder="Select Industry" />
            </SelectTrigger>
            <SelectContent className="bg-[#F9EFEF] border border-[#FF577F] max-h-[300px] overflow-y-auto">
              <SelectItem
                value="Bounding Boxes"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Bounding Boxes
              </SelectItem>
              <SelectItem
                value="Polygon Points"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Polygon Points
              </SelectItem>
              <SelectItem
                value="Segmentation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Segmentation
              </SelectItem>
              <SelectItem
                value="Cuboid"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Cuboid
              </SelectItem>
              <SelectItem
                value="Keypoint"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Keypoint
              </SelectItem>
              <SelectItem
                value="Classification"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Classification
              </SelectItem>
              <SelectItem
                value="Rectangles"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Rectangles
              </SelectItem>
              <SelectItem
                value="Polyline"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Polyline
              </SelectItem>
              <SelectItem
                value="Semantic Segmentation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Semantic Segmentation
              </SelectItem>
              <SelectItem
                value="Transcription"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Transcription
              </SelectItem>
              <SelectItem
                value="Audio Labeling"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Audio Labeling
              </SelectItem>
              <SelectItem
                value="Video Annotation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Video Annotation
              </SelectItem>
              <SelectItem
                value="Lidar Annotation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Lidar Annotation
              </SelectItem>
              <SelectItem
                value="Radiology Annotation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Radiology Annotation
              </SelectItem>
              <SelectItem
                value="RLHF"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                RLHF
              </SelectItem>
              <SelectItem
                value="Medical Annotation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Medical Annotation
              </SelectItem>
              <SelectItem
                value="Data Validation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Data Validation
              </SelectItem>
              <SelectItem
                value="Prompt writing"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Prompt writing
              </SelectItem>
              <SelectItem
                value="Data Curation"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Data Curation
              </SelectItem>
              <SelectItem
                value="Content Rating"
                className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
              >
                Content Rating
              </SelectItem>
            </SelectContent>
          </Select>
          {errors.industry && (
            <p className="text-red-500 text-xs mt-1">
              {errors.industry.message}
            </p>
          )}
        </div>

        {/* Annotation Category and Start Date */}
        <div className="flex gap-4">
          {/* Annotation Category (left side) - Improved Styling */}
          <div className="w-1/2 space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Annotation Category
            </label>
            <Select
              onValueChange={(value) =>
                handleSelectChange("annotationCategory", value)
              }
            >
              <SelectTrigger className="w-full border-gradient bg-[#F9EFEF] hover:bg-[#F9EFEF] focus:ring-2 focus:ring-[#FF577F]">
                <SelectValue placeholder="Select Annotation Category" />
              </SelectTrigger>
              <SelectContent className="bg-[#F9EFEF] border border-[#FF577F] max-h-[300px] overflow-y-auto">
                <SelectItem
                  value="Automobile"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Automobile
                </SelectItem>
                <SelectItem
                  value="BFSI"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  BFSI
                </SelectItem>
                <SelectItem
                  value="Agriculture"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Agriculture
                </SelectItem>
                <SelectItem
                  value="Biosciences"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Biosciences
                </SelectItem>
                <SelectItem
                  value="Medical sciences"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Medical sciences
                </SelectItem>
                <SelectItem
                  value="Marketing"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Marketing
                </SelectItem>
                <SelectItem
                  value="Geospatial"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Geospatial
                </SelectItem>
                <SelectItem
                  value="Retail/ecommerce"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Retail/ecommerce
                </SelectItem>
                <SelectItem
                  value="AI & Technologies"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  AI & Technologies
                </SelectItem>
                <SelectItem
                  value="LLM builder"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  LLM builder
                </SelectItem>
                <SelectItem
                  value="Defense"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Defense
                </SelectItem>
                <SelectItem
                  value="Others"
                  className="hover:bg-[#FFD6E0] focus:bg-[#FF577F] focus:text-white"
                >
                  Others
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.annotationCategory && (
              <p className="text-red-500 text-xs mt-1">
                {errors.annotationCategory.message}
              </p>
            )}
          </div>

          {/* Start Date (right side) */}
          <div className="w-1/2 space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Start Date
            </label>
            <input
              type="date"
              className="w-full border-gradient rounded-md p-2 bg-[#F9EFEF]"
              min={minSelectableDate}
              {...register("startOn")}
            />
          </div>
        </div>

        {/* Preferred Work Duration */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Preferred Work Duration
          </label>
          <div className="flex gap-4">
            <div className="w-1/2">
              <input
                type="time"
                placeholder="HH:MM"
                {...register("fromTime")}
                className="w-full border-gradient rounded-md p-2 bg-[#F9EFEF]"
                pattern="[0-9]{2}:[0-9]{2}"
              />
              {errors.fromTime && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.fromTime.message}
                </p>
              )}
            </div>
            <div className="w-1/2">
              <input
                type="time"
                placeholder="HH:MM"
                {...register("toTime")}
                className="w-full border-gradient rounded-md p-2 bg-[#F9EFEF]"
                pattern="[0-9]{2}:[0-9]{2}"
              />
              {errors.toTime && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.toTime.message}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Description (optional)
          </label>
          <div className="border-gradient rounded-md">
            <Textarea
              {...register("description")}
              placeholder="Description (optional)"
              className="w-full rounded-md p-3 min-h-[150px] resize-none bg-[#F9EFEF]"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <PaymentModal
            register={register}
            handleSubmit={handleSubmit}
            setValue={setValue}
            control={control}
            errors={errors}
            onSubmit={onSubmit}
            formValid={formValid}
            isSubmitting={isSubmitting}
          />
        </div>
      </form>

      {/* Success Toast */}
      {showToast && (
        <CustomToast
          title="Questionnaire Submitted"
          message="Your preferences have been saved successfully. Redirecting to dashboard..."
          type="success"
          onClose={() => setShowToast(false)}
        />
      )}

      {/* Error Toast */}
      {errorMessage && (
        <div className="fixed top-5 right-5 z-[100]">
          <CustomToast
            title="Error"
            message={errorMessage}
            type="error"
            onClose={() => setErrorMessage(null)}
          />
        </div>
      )}
    </div>
  );
};

export default Questioniare;