"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FaDownload, FaSpinner } from "react-icons/fa";
import { generateAttendancePdf, generateSampleAttendanceData } from "@/utils/generateAttendancePdf";

interface DownloadAttendanceButtonProps {
  employeeName?: string;
  employeeId?: string;
  department?: string;
  month?: string;
  year?: string;
  attendanceRecords?: any[];
}

export const DownloadAttendanceButton = ({
  employeeName = "John Doe",
  employeeId = "EMP001",
  department = "Data Annotation",
  month,
  year,
  attendanceRecords
}: DownloadAttendanceButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      // Use sample data if no real data provided
      const attendanceData = attendanceRecords ? {
        employeeName,
        employeeId,
        department,
        month: month || new Date().toLocaleString('default', { month: 'long' }),
        year: year || new Date().getFullYear().toString(),
        records: attendanceRecords,
        totalWorkingDays: attendanceRecords.length,
        totalPresent: attendanceRecords.filter((r: any) => r.status === 'Present').length,
        totalAbsent: attendanceRecords.filter((r: any) => r.status === 'Absent').length,
        totalLate: attendanceRecords.filter((r: any) => r.status === 'Late').length,
        totalHours: "160:00" // Calculate based on actual data
      } : generateSampleAttendanceData();

      await generateAttendancePdf(attendanceData);
    } catch (error) {
      console.error("Download error:", error);
      alert(`Failed to download: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleDownload}
      disabled={isLoading}
      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center space-x-2"
    >
      {isLoading ? (
        <>
          <FaSpinner className="animate-spin" />
          <span>Generating...</span>
        </>
      ) : (
        <>
          <FaDownload />
          <span>Download Attendance Report</span>
        </>
      )}
    </Button>
  );
};
