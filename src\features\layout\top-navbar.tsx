import React from "react";
import logo from "@/assets/darklogo.png";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useNavigate } from "react-router-dom";
import { IoMdArrowDropdown } from "react-icons/io";

interface TopNavbarProps {
  handleNavbar: () => void;
  navbar: boolean;
}

const TopNavbar: React.FC<TopNavbarProps> = () => {
  // Get user from Redux store
  const user = useSelector((state: RootState) => state.auth.user);

  const navigate = useNavigate();

  // Function to determine navigation path based on user role
  const getProfileNavigationPath = (role: string, userId: string) => {
    const encodedUserId = encodeURIComponent(userId);
    switch (role) {
      case "ADMIN":
        return `/admin/user-profile-data/${encodedUserId}`;
      case "PROJECT_COORDINATOR":
        return `/coordinator/user-profile-data/${encodedUserId}`;
      case "ANNOTATOR":
        return `/annotator/user-profile-data/${encodedUserId}`;
      case "COWORKER":
        return `/dashboard/user-profile-data/${encodedUserId}`;
      case "CLIENT":
        return `/dashboard/user-profile-data/${encodedUserId}`;
      default:
        return `/dashboard/user-profile-data/${encodedUserId}`; // Fallback for CLIENT or unknown roles
    }
  };

  return (
    <div className="border-b-2 w-full bg-white">
      <div className="flex items-center justify-between lg:px-3 xl:px-5 2xl:px-6">
        {/* Logo */}
        <div className="flex gap-5 items-center">
          <div className="flex justify-center border-r-2 lg:py-1 lg:px-[2rem] lg:w-[208px] xl:py-1 xl:px-[2rem] xl:w-[230px] 2xl:py-2 2xl:px-[1.95rem] 2xl:w-[256px]">
            <img
              className="w-[185px] h-[75px]"
              src={logo}
              alt="Logo"
            />
          </div>
        </div>

        {/* Profile section */}
        {user && (
          <div
            className="flex flex-row items-center mr-10 cursor-pointer"
            onClick={() => navigate(getProfileNavigationPath(user.role, user.id))}
          >
            <img
              src={user.profilePic}
              alt={`${user.name}'s profile`}
              width={40}
              height={40}
              className="rounded-full object-cover w-8 h-8"
            />
            <IoMdArrowDropdown className="text-[#5e5e5e]" />
          </div>
        )}
      </div>
    </div>
  );
};

export default TopNavbar;