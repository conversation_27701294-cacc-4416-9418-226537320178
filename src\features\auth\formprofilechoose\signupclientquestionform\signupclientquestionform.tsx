import React, { useState, useEffect } from "react";
import { featureLabels } from "@/features/clientdashboard/add-on/component/commonpackageplan/featureLabels"; // Import feature labels
// Import ToggleSection
import PlanTable from "../componets/packageplan"; // Import PlanTable
import { getAllPackages } from "@/features/admindashboard/packageadmin/component/package/api_package/api_package.tsx";
import img1 from "@/assets/darklogo.png";

// Define the Plan type
interface Plan {
  name: string;
  price: number;
  features: boolean[];
  id: string;
  isActive: boolean;
  description?: string;
  billingType?: string;
}

const SignupClientQuestionForm: React.FC = () => {
  // const [selected, setSelected] = useState<"ongoing" | "new">("ongoing"); // State for the selected project type
  const [packages, setPackages] = useState<Plan[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch packages from the API
    const fetchPackages = async () => {
      try {
        setLoading(true);
        const response = await getAllPackages();

        if (
          response &&
          response.data &&
          Array.isArray(response.data.packages)
        ) {
          // Filter only active packages
          const activePackages = response.data.packages
            .filter((pkg: any) => pkg.isActive === true)
            .map((pkg: any) => ({
              id: pkg.id,
              name: pkg.name,
              price: pkg.price,
              description: pkg.description,
              isActive: pkg.isActive,
              billingType: pkg.billingType,
              // Generate random features for now - in a real app, these would come from the API
              features: featureLabels.map(() => Math.random() > 0.3),
            }));

          setPackages(activePackages);
        }
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  return (
    <div className="w-full">
      <div className="border-b-2 py-3">
        <div className="w-full px-6">
          <img src={img1} className="w-[185px] h-[75px]" />
        </div>
      </div>
      <div className="flex flex-col items-center justify-center gap-8">
        {/* Toggle Section */}
        {/* <ToggleSection selected={selected} setSelected={setSelected} /> */}
        <div className="flex flex-col justify-start items-center mt-3">
          <h1 className="text-[30px] font-semibold font-poppins text-[#282828]">
            Choose Plan as per your demand
          </h1>
          <p className="w-[60%] text-center text-[18px] text-[#4B4B4B] font-normal">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec
            rhoncus rhoncus mi, ut ornare lacus posuere non.
          </p>
        </div>

        {/* Annotator Package Table Section */}
        {loading ? (
          <div className="flex justify-center items-center h-64 lg:h-80 xl:h-96 2xl:h-[30rem]">
            <div className="animate-spin rounded-full h-10 w-10 lg:h-12 lg:w-12 xl:h-14 xl:w-14 2xl:h-16 2xl:w-16 border-t-2 border-b-2 border-[#FF577F]"></div>
          </div>
        ) : (
          <div className="overflow-x-auto CustomScroll">
            <div className="min-w-max lg:min-w-0">
              <PlanTable plans={packages} featureLabels={featureLabels} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SignupClientQuestionForm;
