import SignupForm from "./common/signupform";
import Logo from "@/assets/darklogo.png";
import { useResponsive } from "@/hooks/use-responsive";
import { AuthCommonComponent } from "@/features/auth/routes/common/AuthCommon";

export default function SignupPage() {
  const { isLaptopMd, isLaptopLg } = useResponsive();

  // Get responsive styles based on screen size
  const getStyles = () => {
    if (isLaptopLg) {
      // 4K/2560px
      return {
        container:
          "w-full flex items-center justify-center overflow-hidden signup-container",
        wrapper: "w-full transition-transform duration-300 ",
        layout: "flex flex-row justify-between w-full h-screen",
        logoContainer: "h-screen p-3 flex flex-col pt-2",
        logoSize: "w-[185px] h-[75px]",
        contentContainer: "flex flex-row w-full gap-24",
        formContainer: "w-[40%] flex items-center justify-center signup-form",
        backgroundContainer: "w-[55%] h-screen",
      };
    } else if (isLaptopMd) {
      // 1440px
      return {
        container: "w-full flex items-center justify-center overflow-hidden s",
        wrapper: "w-full transition-transform duration-300 ",
        layout: "flex flex-row justify-between w-full",
        logoContainer: "h-screen p-3 flex flex-col pt-2",
        logoSize: "w-[185px] h-[75px]",
        contentContainer: "flex flex-row w-full gap-16",
        formContainer: "w-[44%] flex items-center signup-form",
        backgroundContainer: "w-[48%]",
      };
    } else {
      // 1024px (default)
      return {
        container: "w-full flex items-center justify-center overflow-hidden ",
        wrapper: "w-full transition-transform duration-300",
        layout: "flex flex-row justify-between w-full",
        logoContainer: "h-screen p-3 flex flex-col pt-2",
        logoSize: "w-[185px] h-[75px]",
        contentContainer: "flex flex-row w-full gap-8",
        formContainer: "w-[46%] flex items-center signup-form",
        backgroundContainer: "w-[46%]",
      };
    }
  };

  const styles = getStyles();

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div className={styles.layout}>
          {/* Logo Container */}
          <div className={styles.logoContainer}>
            <img src={Logo} alt="Logo" className={styles.logoSize} />
          </div>

          {/* Main Content Container */}
          <div className={styles.contentContainer}>
            {/* Left Side - Form */}
            <div className={styles.formContainer}>
              <SignupForm />
            </div>

            {/* Right Side - Background Component */}
            <div className={styles.backgroundContainer}>
              <AuthCommonComponent />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
