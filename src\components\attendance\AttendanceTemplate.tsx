import React from 'react';

interface AttendanceRecord {
  date: string;
  checkIn: string;
  checkOut: string;
  totalHours: string;
  status: 'Present' | 'Absent' | 'Late' | 'Half Day';
  project?: string;
}

interface AttendanceTemplateProps {
  attendanceData: {
    employeeName: string;
    employeeId: string;
    department: string;
    month: string;
    year: string;
    records: AttendanceRecord[];
    totalWorkingDays: number;
    totalPresent: number;
    totalAbsent: number;
    totalLate: number;
    totalHours: string;
  };
}

export const AttendanceTemplate: React.FC<AttendanceTemplateProps> = ({ attendanceData }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Present': return 'text-green-600 bg-green-50';
      case 'Absent': return 'text-red-600 bg-red-50';
      case 'Late': return 'text-yellow-600 bg-yellow-50';
      case 'Half Day': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Present': return '✓';
      case 'Absent': return '✗';
      case 'Late': return '⚠';
      case 'Half Day': return '◐';
      default: return '-';
    }
  };

  return (
    <div className="w-[210mm] min-h-[297mm] bg-white p-8 font-sans text-black print:shadow-none" 
         style={{ fontSize: '11px', lineHeight: '1.4' }}>
      
      {/* Header Section */}
      <div className="border-b-4 border-blue-600 pb-6 mb-8">
        <div className="flex justify-between items-start">
          {/* Company Logo & Info */}
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">M</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Macgence Technologies</h1>
              <p className="text-sm text-gray-600">Professional Data Annotation Services</p>
            </div>
          </div>
          
          {/* Report Title */}
          <div className="text-right">
            <h2 className="text-3xl font-bold text-blue-600">ATTENDANCE REPORT</h2>
            <p className="text-sm text-gray-600 mt-2">Generated on {new Date().toLocaleDateString('en-GB')}</p>
          </div>
        </div>
      </div>

      {/* Employee Information */}
      <div className="grid grid-cols-2 gap-6 mb-8">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border-l-4 border-blue-500">
          <h3 className="text-lg font-bold text-blue-700 mb-4">Employee Information</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Name:</span>
              <span className="font-semibold">{attendanceData.employeeName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Employee ID:</span>
              <span className="font-semibold">{attendanceData.employeeId}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Department:</span>
              <span className="font-semibold">{attendanceData.department}</span>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg border-l-4 border-green-500">
          <h3 className="text-lg font-bold text-green-700 mb-4">Report Period</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Month:</span>
              <span className="font-semibold">{attendanceData.month}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Year:</span>
              <span className="font-semibold">{attendanceData.year}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total Days:</span>
              <span className="font-semibold">{attendanceData.totalWorkingDays}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-4 gap-4 mb-8">
        <div className="bg-green-50 p-4 rounded-lg border border-green-200 text-center">
          <div className="text-2xl font-bold text-green-600">{attendanceData.totalPresent}</div>
          <div className="text-sm text-green-700 font-medium">Present Days</div>
        </div>
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 text-center">
          <div className="text-2xl font-bold text-red-600">{attendanceData.totalAbsent}</div>
          <div className="text-sm text-red-700 font-medium">Absent Days</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 text-center">
          <div className="text-2xl font-bold text-yellow-600">{attendanceData.totalLate}</div>
          <div className="text-sm text-yellow-700 font-medium">Late Days</div>
        </div>
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 text-center">
          <div className="text-2xl font-bold text-blue-600">{attendanceData.totalHours}</div>
          <div className="text-sm text-blue-700 font-medium">Total Hours</div>
        </div>
      </div>

      {/* Attendance Table */}
      <div className="mb-8">
        <h3 className="text-lg font-bold text-gray-800 mb-4">Daily Attendance Records</h3>
        <div className="overflow-hidden rounded-lg shadow-sm border">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gradient-to-r from-gray-700 to-gray-800 text-white">
                <th className="border border-gray-600 p-3 text-left text-xs font-bold">Date</th>
                <th className="border border-gray-600 p-3 text-left text-xs font-bold">Check In</th>
                <th className="border border-gray-600 p-3 text-left text-xs font-bold">Check Out</th>
                <th className="border border-gray-600 p-3 text-left text-xs font-bold">Total Hours</th>
                <th className="border border-gray-600 p-3 text-left text-xs font-bold">Status</th>
                <th className="border border-gray-600 p-3 text-left text-xs font-bold">Project</th>
              </tr>
            </thead>
            <tbody>
              {attendanceData.records.map((record, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="border border-gray-300 p-3 text-xs font-medium">
                    {new Date(record.date).toLocaleDateString('en-GB')}
                  </td>
                  <td className="border border-gray-300 p-3 text-xs">
                    {record.checkIn || '-'}
                  </td>
                  <td className="border border-gray-300 p-3 text-xs">
                    {record.checkOut || '-'}
                  </td>
                  <td className="border border-gray-300 p-3 text-xs font-medium">
                    {record.totalHours || '-'}
                  </td>
                  <td className="border border-gray-300 p-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                      <span className="mr-1">{getStatusIcon(record.status)}</span>
                      {record.status}
                    </span>
                  </td>
                  <td className="border border-gray-300 p-3 text-xs">
                    {record.project || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Performance Analysis */}
      <div className="grid grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-50 p-6 rounded-lg border">
          <h3 className="text-lg font-bold text-gray-800 mb-4">Attendance Percentage</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Present Rate:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-200 rounded-full">
                  <div 
                    className="h-2 bg-green-500 rounded-full" 
                    style={{ width: `${(attendanceData.totalPresent / attendanceData.totalWorkingDays) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-bold text-green-600">
                  {((attendanceData.totalPresent / attendanceData.totalWorkingDays) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Punctuality:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-gray-200 rounded-full">
                  <div 
                    className="h-2 bg-blue-500 rounded-full" 
                    style={{ width: `${((attendanceData.totalPresent - attendanceData.totalLate) / attendanceData.totalPresent) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-bold text-blue-600">
                  {(((attendanceData.totalPresent - attendanceData.totalLate) / attendanceData.totalPresent) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h3 className="text-lg font-bold text-blue-700 mb-4">Notes & Remarks</h3>
          <div className="text-xs text-gray-600 space-y-2">
            <p>• This report is generated automatically from the attendance system.</p>
            <p>• All times are recorded in local timezone (IST).</p>
            <p>• Late arrival is marked if check-in is after 9:30 AM.</p>
            <p>• Half day is marked for less than 4 hours of work.</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t pt-6 mt-auto">
        <div className="flex justify-between items-center text-xs text-gray-600">
          <div>
            <p className="font-semibold">Macgence Technologies Private Limited</p>
            <p>7th Floor, Platina Heights C-24, Sector 62, Noida, UP 201301</p>
          </div>
          <div className="text-right">
            <p>Report Generated: {new Date().toLocaleString('en-GB')}</p>
            <p className="font-semibold">HR Department</p>
          </div>
        </div>
      </div>
    </div>
  );
};
