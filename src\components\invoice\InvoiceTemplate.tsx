import React from 'react';

interface InvoiceTemplateProps {
  invoiceData: {
    paymentId: string;
    amount: string;
    date: string;
    customerName: string;
    customerEmail: string;
    packageName: string;
    price: string;
  };
}

export const InvoiceTemplate: React.FC<InvoiceTemplateProps> = ({ invoiceData }) => {
  const generateInvoiceNumber = () => {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const nextYear = (date.getFullYear() + 1).toString().slice(-2);
    const randomNum = Math.floor(Math.random() * 99999).toString().padStart(5, '0');
    return `INV${year}/${nextYear}-${randomNum}`;
  };

  const convertAmountToWords = (amount: string) => {
    const num = parseInt(amount);
    if (num === 300) return "United States Dollar Three Hundred";
    // Add more conversions as needed
    return `United States Dollar ${num}`;
  };

  return (
    <div className="w-[210mm] min-h-[297mm] bg-white p-8 font-sans text-black print:shadow-none"
         style={{ fontSize: '11px', lineHeight: '1.4' }}>

      {/* Header Section */}
      <div className="flex justify-between items-start mb-10">
        {/* Left Side - Company Details */}
        <div className="flex-1">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mb-3">
              <span className="text-white font-bold text-lg">M</span>
            </div>
          </div>
          <div className="text-xl font-bold mb-3 text-gray-800">Macgence</div>
          <div className="text-xs space-y-1 text-gray-600 leading-relaxed">
            <div className="font-semibold text-gray-800">Macgence Technologies Private Limited</div>
            <div>CN UF22000UP2022PTC164392</div>
            <div>7th Floor, Platina Heights C - 24 Sector 62</div>
            <div>Noida Uttar Pradesh 201301</div>
            <div>India</div>
            <div className="font-medium">GSTIN: 09AAPCH4735A1Z9</div>
          </div>
        </div>

        {/* Right Side - Invoice Details */}
        <div className="text-right">
          <h1 className="text-4xl font-bold mb-6 text-blue-600">TAX INVOICE</h1>
          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="text-sm mb-4">
              <div className="font-semibold text-gray-700">Invoice# {generateInvoiceNumber()}</div>
            </div>
            <div className="mb-4">
              <div className="text-sm font-semibold text-gray-600">Balance Due</div>
              <div className="text-2xl font-bold text-red-600">${invoiceData.amount}</div>
            </div>
            <div className="text-xs space-y-2">
              <div className="flex justify-between min-w-[200px]">
                <span className="text-gray-600">Invoice Date:</span>
                <span className="font-medium">{new Date(invoiceData.date).toLocaleDateString('en-GB')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Terms:</span>
                <span className="font-medium">Net 4</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Due Date:</span>
                <span className="font-medium">{new Date(new Date(invoiceData.date).getTime() + 4 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To Section */}
      <div className="mb-8">
        <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
          <div className="font-bold text-sm mb-3 text-blue-700">Bill To</div>
          <div className="text-sm space-y-1">
            <div className="font-semibold text-gray-800">{invoiceData.customerName}</div>
            <div className="text-gray-600">{invoiceData.customerEmail}</div>
            <div className="text-gray-600">Israel</div>
          </div>
        </div>
      </div>

      {/* Invoice Table */}
      <div className="mb-8">
        <table className="w-full border-collapse shadow-sm rounded-lg overflow-hidden">
          <thead>
            <tr className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <th className="border border-blue-400 p-3 text-left text-xs font-bold w-8">#</th>
              <th className="border border-blue-400 p-3 text-left text-xs font-bold">Item & Description</th>
              <th className="border border-blue-400 p-3 text-left text-xs font-bold w-20">HSN/SAC</th>
              <th className="border border-blue-400 p-3 text-left text-xs font-bold w-20">Qty</th>
              <th className="border border-blue-400 p-3 text-left text-xs font-bold w-16">Rate</th>
              <th className="border border-blue-400 p-3 text-left text-xs font-bold w-20">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-white hover:bg-gray-50">
              <td className="border border-gray-300 p-3 text-xs">1</td>
              <td className="border border-gray-300 p-3 text-xs">
                <div className="font-medium">{invoiceData.packageName}</div>
                <div className="text-gray-500">(Professional Annotation Services)</div>
              </td>
              <td className="border border-gray-300 p-3 text-xs">998313</td>
              <td className="border border-gray-300 p-3 text-xs">1.00</td>
              <td className="border border-gray-300 p-3 text-xs">${invoiceData.amount}</td>
              <td className="border border-gray-300 p-3 text-xs font-medium">${invoiceData.amount}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Totals Section */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="bg-gray-50 p-4 rounded-lg border">
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Sub Total</span>
                <span className="font-medium">${invoiceData.amount}</span>
              </div>
              <div className="flex justify-between border-t pt-3">
                <span className="font-bold text-lg">Total</span>
                <span className="font-bold text-lg text-blue-600">${invoiceData.amount}</span>
              </div>
              <div className="flex justify-between bg-red-50 p-2 rounded border-l-4 border-red-500">
                <span className="font-bold text-red-700">Balance Due</span>
                <span className="font-bold text-red-700">${invoiceData.amount}</span>
              </div>
            </div>

            <div className="mt-6 p-3 bg-blue-50 rounded border">
              <div className="text-xs font-semibold text-blue-700 mb-1">Total In Words:</div>
              <div className="text-xs italic text-blue-600">{convertAmountToWords(invoiceData.amount)}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto border-t pt-6">
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 rounded-lg">
          <div className="text-sm font-semibold text-gray-800 mb-4">Thanks for your business!</div>
          <div className="text-xs text-gray-600 mb-4">
            <strong>PayPal account:</strong> <EMAIL>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-600">
            <div>
              <div className="font-semibold text-gray-700 mb-2">Account Details:</div>
              <div>Account Holder: Macgence Technologies Private Limited</div>
              <div>Payment Method: SWIFT (International Wire)</div>
              <div>IBAN: **********************</div>
              <div>BIC/SWIFT: TCCLGB3L</div>
            </div>
            <div>
              <div className="font-semibold text-gray-700 mb-2">Bank Details:</div>
              <div>Bank Name: The Currency Cloud Limited</div>
              <div>Bank Address: 12, Steward Street,</div>
              <div>The Steward Building, London, E1 6FQ,</div>
              <div>Great Britain, United Kingdom</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
