import { createRoot } from 'react-dom/client';
import { InvoiceTemplate } from '@/components/invoice/InvoiceTemplate';

interface InvoiceData {
  provider: string;
  paymentId: string;
  amount: string;
  status: string;
  paymentMethod: string;
  date: string;
  customerName: string;
  customerEmail: string;
  packageName: string;
  price: string;
}

// Main PDF generation function using html2canvas
export const generateInvoicePdf = async (invoiceData: InvoiceData) => {
  const html2canvas = (await import('html2canvas')).default;
  const jsPDF = (await import('jspdf')).jsPDF;

  // Create a temporary container
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  container.style.width = '210mm';
  container.style.height = 'auto';
  document.body.appendChild(container);

  // Create root and render the component
  const root = createRoot(container);

  return new Promise<void>((resolve, reject) => {
    try {
      root.render(
        <InvoiceTemplate
          invoiceData={{
            paymentId: invoiceData.paymentId,
            amount: invoiceData.amount,
            date: invoiceData.date,
            customerName: invoiceData.customerName,
            customerEmail: invoiceData.customerEmail,
            packageName: invoiceData.packageName,
            price: invoiceData.price
          }}
        />
      );

      // Wait for render to complete
      setTimeout(async () => {
        try {
          const canvas = await html2canvas(container, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: 794, // A4 width in pixels at 96 DPI
            height: 1123 // A4 height in pixels at 96 DPI
          });

          const imgData = canvas.toDataURL('image/png');
          const pdf = new jsPDF('p', 'mm', 'a4');

          const imgWidth = 210;
          const pageHeight = 295;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          let heightLeft = imgHeight;
          let position = 0;

          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;

          while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
          }

          pdf.save(`TAX_INVOICE_${invoiceData.paymentId}.pdf`);

          // Cleanup
          root.unmount();
          document.body.removeChild(container);
          resolve();
        } catch (error) {
          console.error('PDF generation error:', error);
          root.unmount();
          document.body.removeChild(container);
          reject(error);
        }
      }, 500);
    } catch (error) {
      console.error('Render error:', error);
      root.unmount();
      document.body.removeChild(container);
      reject(error);
    }
  });
};
