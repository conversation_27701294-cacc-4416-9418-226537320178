import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useCreateSubscriptionMutation } from "@/features/clientdashboard/api/mutation";
// import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { clientProfileShow } from "@/features/layout/clientprofile/profileclient_api/Clientprofile_api";
import { BackButton } from "@/_components/common";


// Country list with ISO codes
const countries = [
  { name: "Afghanistan", code: "AF" },
  { name: "Albania", code: "AL" },
  { name: "Algeria", code: "DZ" },
  { name: "Andorra", code: "AD" },
  { name: "Angola", code: "AO" },
  { name: "Argentina", code: "AR" },
  { name: "Armenia", code: "AM" },
  { name: "Australia", code: "AU" },
  { name: "Austria", code: "AT" },
  { name: "Azerbaijan", code: "AZ" },
  { name: "Bahamas", code: "BS" },
  { name: "Bahrain", code: "BH" },
  { name: "Bangladesh", code: "BD" },
  { name: "Barbados", code: "BB" },
  { name: "Belarus", code: "BY" },
  { name: "Belgium", code: "BE" },
  { name: "Belize", code: "BZ" },
  { name: "Benin", code: "BJ" },
  { name: "Bhutan", code: "BT" },
  { name: "Bolivia", code: "BO" },
  { name: "Bosnia and Herzegovina", code: "BA" },
  { name: "Botswana", code: "BW" },
  { name: "Brazil", code: "BR" },
  { name: "Brunei", code: "BN" },
  { name: "Bulgaria", code: "BG" },
  { name: "Burkina Faso", code: "BF" },
  { name: "Burundi", code: "BI" },
  { name: "Cambodia", code: "KH" },
  { name: "Cameroon", code: "CM" },
  { name: "Canada", code: "CA" },
  { name: "Cape Verde", code: "CV" },
  { name: "Central African Republic", code: "CF" },
  { name: "Chad", code: "TD" },
  { name: "Chile", code: "CL" },
  { name: "China", code: "CN" },
  { name: "Colombia", code: "CO" },
  { name: "Comoros", code: "KM" },
  { name: "Congo", code: "CG" },
  { name: "Costa Rica", code: "CR" },
  { name: "Croatia", code: "HR" },
  { name: "Cuba", code: "CU" },
  { name: "Cyprus", code: "CY" },
  { name: "Czech Republic", code: "CZ" },
  { name: "Denmark", code: "DK" },
  { name: "Djibouti", code: "DJ" },
  { name: "Dominica", code: "DM" },
  { name: "Dominican Republic", code: "DO" },
  { name: "Ecuador", code: "EC" },
  { name: "Egypt", code: "EG" },
  { name: "El Salvador", code: "SV" },
  { name: "Equatorial Guinea", code: "GQ" },
  { name: "Eritrea", code: "ER" },
  { name: "Estonia", code: "EE" },
  { name: "Ethiopia", code: "ET" },
  { name: "Fiji", code: "FJ" },
  { name: "Finland", code: "FI" },
  { name: "France", code: "FR" },
  { name: "Gabon", code: "GA" },
  { name: "Gambia", code: "GM" },
  { name: "Georgia", code: "GE" },
  { name: "Germany", code: "DE" },
  { name: "Ghana", code: "GH" },
  { name: "Greece", code: "GR" },
  { name: "Grenada", code: "GD" },
  { name: "Guatemala", code: "GT" },
  { name: "Guinea", code: "GN" },
  { name: "Guinea-Bissau", code: "GW" },
  { name: "Guyana", code: "GY" },
  { name: "Haiti", code: "HT" },
  { name: "Honduras", code: "HN" },
  { name: "Hungary", code: "HU" },
  { name: "Iceland", code: "IS" },
  { name: "India", code: "IN" },
  { name: "Indonesia", code: "ID" },
  { name: "Iran", code: "IR" },
  { name: "Iraq", code: "IQ" },
  { name: "Ireland", code: "IE" },
  { name: "Israel", code: "IL" },
  { name: "Italy", code: "IT" },
  { name: "Jamaica", code: "JM" },
  { name: "Japan", code: "JP" },
  { name: "Jordan", code: "JO" },
  { name: "Kazakhstan", code: "KZ" },
  { name: "Kenya", code: "KE" },
  { name: "Kiribati", code: "KI" },
  { name: "North Korea", code: "KP" },
  { name: "South Korea", code: "KR" },
  { name: "Kuwait", code: "KW" },
  { name: "Kyrgyzstan", code: "KG" },
  { name: "Laos", code: "LA" },
  { name: "Latvia", code: "LV" },
  { name: "Lebanon", code: "LB" },
  { name: "Lesotho", code: "LS" },
  { name: "Liberia", code: "LR" },
  { name: "Libya", code: "LY" },
  { name: "Liechtenstein", code: "LI" },
  { name: "Lithuania", code: "LT" },
  { name: "Luxembourg", code: "LU" },
  { name: "Macedonia", code: "MK" },
  { name: "Madagascar", code: "MG" },
  { name: "Malawi", code: "MW" },
  { name: "Malaysia", code: "MY" },
  { name: "Maldives", code: "MV" },
  { name: "Mali", code: "ML" },
  { name: "Malta", code: "MT" },
  { name: "Marshall Islands", code: "MH" },
  { name: "Mauritania", code: "MR" },
  { name: "Mauritius", code: "MU" },
  { name: "Mexico", code: "MX" },
  { name: "Micronesia", code: "FM" },
  { name: "Moldova", code: "MD" },
  { name: "Monaco", code: "MC" },
  { name: "Mongolia", code: "MN" },
  { name: "Montenegro", code: "ME" },
  { name: "Morocco", code: "MA" },
  { name: "Mozambique", code: "MZ" },
  { name: "Myanmar", code: "MM" },
  { name: "Namibia", code: "NA" },
  { name: "Nauru", code: "NR" },
  { name: "Nepal", code: "NP" },
  { name: "Netherlands", code: "NL" },
  { name: "New Zealand", code: "NZ" },
  { name: "Nicaragua", code: "NI" },
  { name: "Niger", code: "NE" },
  { name: "Nigeria", code: "NG" },
  { name: "Norway", code: "NO" },
  { name: "Oman", code: "OM" },
  { name: "Pakistan", code: "PK" },
  { name: "Palau", code: "PW" },
  { name: "Panama", code: "PA" },
  { name: "Papua New Guinea", code: "PG" },
  { name: "Paraguay", code: "PY" },
  { name: "Peru", code: "PE" },
  { name: "Philippines", code: "PH" },
  { name: "Poland", code: "PL" },
  { name: "Portugal", code: "PT" },
  { name: "Qatar", code: "QA" },
  { name: "Romania", code: "RO" },
  { name: "Russia", code: "RU" },
  { name: "Rwanda", code: "RW" },
  { name: "Saint Kitts and Nevis", code: "KN" },
  { name: "Saint Lucia", code: "LC" },
  { name: "Saint Vincent and the Grenadines", code: "VC" },
  { name: "Samoa", code: "WS" },
  { name: "San Marino", code: "SM" },
  { name: "Sao Tome and Principe", code: "ST" },
  { name: "Saudi Arabia", code: "SA" },
  { name: "Senegal", code: "SN" },
  { name: "Serbia", code: "RS" },
  { name: "Seychelles", code: "SC" },
  { name: "Sierra Leone", code: "SL" },
  { name: "Singapore", code: "SG" },
  { name: "Slovakia", code: "SK" },
  { name: "Slovenia", code: "SI" },
  { name: "Solomon Islands", code: "SB" },
  { name: "Somalia", code: "SO" },
  { name: "South Africa", code: "ZA" },
  { name: "South Sudan", code: "SS" },
  { name: "Spain", code: "ES" },
  { name: "Sri Lanka", code: "LK" },
  { name: "Sudan", code: "SD" },
  { name: "Suriname", code: "SR" },
  { name: "Swaziland", code: "SZ" },
  { name: "Sweden", code: "SE" },
  { name: "Switzerland", code: "CH" },
  { name: "Syria", code: "SY" },
  { name: "Taiwan", code: "TW" },
  { name: "Tajikistan", code: "TJ" },
  { name: "Tanzania", code: "TZ" },
  { name: "Thailand", code: "TH" },
  { name: "Togo", code: "TG" },
  { name: "Tonga", code: "TO" },
  { name: "Trinidad and Tobago", code: "TT" },
  { name: "Tunisia", code: "TN" },
  { name: "Turkey", code: "TR" },
  { name: "Turkmenistan", code: "TM" },
  { name: "Tuvalu", code: "TV" },
  { name: "Uganda", code: "UG" },
  { name: "Ukraine", code: "UA" },
  { name: "United Arab Emirates", code: "AE" },
  { name: "United Kingdom", code: "GB" },
  { name: "United States", code: "US" },
  { name: "Uruguay", code: "UY" },
  { name: "Uzbekistan", code: "UZ" },
  { name: "Vanuatu", code: "VU" },
  { name: "Vatican City", code: "VA" },
  { name: "Venezuela", code: "VE" },
  { name: "Vietnam", code: "VN" },
  { name: "Yemen", code: "YE" },
  { name: "Zambia", code: "ZM" },
  { name: "Zimbabwe", code: "ZW" },
];




// Type for form values
export type BillingFormValues = z.infer<typeof billingSchema>;

// Type for location state
interface LocationState {
  formData: {
    availableFrom: string;
    availableTo: string;
    packageId: string;
    paymentMethod: string;
    timezone: string;
    industry: string;
    category: string;
    description: string;
    startOn: string;
  };
}


// Updated Zod schema with all required fields
export const billingSchema = z.object({
  sameAsBusiness: z.boolean().default(false),
  billing: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zipcode: z.string()
      .min(6, "Postal code must be at least 6 digits")
      .regex(/^\d+$/, "Postal code must contain only numbers"),
    country: z.string().min(1, "Country is required"),
  }),
});
export default function AddonBillingForm() {
  // Use type assertion
    const { state } = useLocation() as { state: LocationState };
    const navigate = useNavigate();
    const { mutate: createSubscription, isPending } = useCreateSubscriptionMutation();
    const [loadingProfile, setLoadingProfile] = useState(false);
  
    const form = useForm<BillingFormValues>({
      resolver: zodResolver(billingSchema),
      defaultValues: {
        sameAsBusiness: false,
        billing: {
          street: "",
          city: "",
          state: "",
          zipcode: "",
          country: "",
        },
      },
    });
  
    useEffect(() => {
      if (!state?.formData) {
        navigate("/dashboard/blank-page-after-payment-successful");
      }
    }, [state, navigate]);
  
    const sameAsBusiness = form.watch("sameAsBusiness");
  
    useEffect(() => {
      const fetchAndFillProfile = async () => {
        if (sameAsBusiness) {
          try {
            setLoadingProfile(true);
            const response = await clientProfileShow();
  
            if (response.profile) {
              form.setValue("billing.city", response.profile.address || "");
              form.setValue("billing.state", response.profile.stateProvince || "");
              form.setValue("billing.zipcode", response.profile.postalCode || "");
            }
          } catch (error) {
            console.error("Failed to fetch profile details:", error);
            form.setValue("sameAsBusiness", false);
          } finally {
            setLoadingProfile(false);
          }
        }
      };
  
      fetchAndFillProfile();
    }, [sameAsBusiness, form]);
  
    const onSubmit = (data: BillingFormValues) => {
     if (state?.formData?.paymentMethod === "bank") {
      navigate("/dashboard/addbank-transfer-payment", {
        state: {
          formData: state.formData, // Contains questionnaire data
          billingData: data.billing, // Contains billing address
        },
      });
    } else {
        // For other payment methods (dodo, paypal), proceed with normal mutation
        createSubscription({
          ...state.formData,
          billing: data.billing,
        });
      }
    };
  return (
    <div className="flex flex-col">
      <div className="flex flex-row gap-1 justify-start items-center">
        <BackButton />
        <p className="text-[#333333] text-[20px] font-medium">Change Payment</p>
      </div>
      <div className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-3xl bg-white rounded-lg shadow-sm p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <h2 className="text-xl font-semibold mb-4 text-[#333333]">
                Billing Address
              </h2>

              {/* <FormField
                control={form.control}
                name="sameAsBusiness"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroup
                        className="flex items-center space-x-2"
                        onValueChange={(value) => field.onChange(value === "true")}
                        value={field.value ? "true" : "false"}
                        disabled={loadingProfile}
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="true"
                            id="same-address"
                            className="text-[#FF577F] text-xl w-5 h-5 bg-[#ffffff] border-[#FF577F] p-1"
                          />
                          <FormLabel
                            htmlFor="same-address"
                            className="font-normal text-[#333333]"
                          >
                            Same as Business Address
                            {loadingProfile && (
                              <span className="ml-2 text-sm text-gray-500">Loading...</span>
                            )}
                          </FormLabel>
                        </div>
                      </RadioGroup>
                    </div>
                  </FormItem>
                )}
              /> */}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <FormField
                    control={form.control}
                    name="billing.country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#333333] font-medium">
                          Country
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="bg-white w-full border-gradient rounded-lg h-11">
                              <SelectValue placeholder="Select Country" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="max-h-80 bg-white border border-[#E5E7EB]">
                            {countries.map((country) => (
                              <SelectItem
                                key={country.code}
                                value={country.code}
                              >
                                {country.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-[#FF577F]" />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="billing.city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#333333] font-medium">
                          City
                        </FormLabel>
                        <FormControl>
                          <div className="border-gradient rounded-lg">
                            <Input
                              placeholder="Enter City"
                              {...field}
                              className="bg-white rounded-md h-11"
                              disabled={sameAsBusiness || loadingProfile}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-[#FF577F]" />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="billing.state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#333333] font-medium">
                          State
                        </FormLabel>
                        <FormControl>
                          <div className="border-gradient rounded-lg">
                            <Input
                              placeholder="Enter State"
                              {...field}
                              className="bg-white rounded-md h-11"
                              disabled={sameAsBusiness || loadingProfile}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-[#FF577F]" />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="billing.zipcode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#333333] font-medium">
                          Postal Code <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="border-gradient rounded-lg">
                            <Input
                              placeholder="Enter Postal Code (numbers only)"
                              {...field}
                              className="bg-white rounded-md h-11"
                              disabled={sameAsBusiness || loadingProfile}
                              onKeyPress={(e) => {
                                if (!/[0-9]/.test(e.key)) {
                                  e.preventDefault();
                                }
                              }}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-[#FF577F]" />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="billing.street"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-[#333333] font-medium">
                          Street Address
                        </FormLabel>
                        <FormControl>
                          <div className="border-gradient rounded-lg">
                            <Input
                              placeholder="Enter Street Address"
                              {...field}
                              className="bg-white rounded-md h-11"
                              disabled={loadingProfile}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-[#FF577F]" />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="mt-8">
                <Button
                  variant={"gradient"}
                  type="submit"
                  className="bg-[#FF577F] hover:bg-[#FF577F]/90 text-white font-medium py-2 px-6 rounded-md transition-colors w-full md:w-auto"
                  disabled={isPending || loadingProfile}
                >
                  {isPending ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </div>
                  ) : (
                    "Submit Billing Info"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}