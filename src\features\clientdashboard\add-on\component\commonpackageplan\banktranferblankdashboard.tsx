
import fourcirclegif from "@/assets/icons/circlesfour.gif";
import { Button } from "@/components/ui/button";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { customAxios } from "@/utils/axio-interceptor";
import { Loader2 } from "lucide-react";
import { useAppSelector, useAppDispatch } from "@/store/hooks/reduxHooks";
import { redirectToDashboard } from "@/utils/navigaterole";
import { setUser, setIsAuthenticated } from "@/store/slices/authSlice";
import { setUserProfile } from "@/store/slices/userSlice";
import logo from "@/assets/darklogo.png"


const NewBlankDashboard = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const userRole = useAppSelector((state) => state.auth.user?.role);
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  const accessToken = useAppSelector((state) => state.auth.accessToken);
  const user = useAppSelector((state) => state.auth.user);
  //  const [navbar, setNavbar] = useState(false);
    // const handleNavbar = () => setNavbar(!navbar);

  // Debug logs for authentication state
  console.log("Bank Transfer Dashboard - Auth State:", {
    isAuthenticated,
    user,
    userRole,
    accessToken: !!accessToken,
    hasTokenInStorage: !!localStorage.getItem("token"),
    hasUserInStorage: !!localStorage.getItem("user")
  });

  // Prevent direct URL access - this component is for bantransfer-blank-page (outside dashboard)
  useEffect(() => {
    if (!location.pathname.endsWith("bantransfer-blank-page")) {
      navigate("/bantransfer-blank-page", {
        replace: true,
      });
    }
  }, [location.pathname, navigate]);

  // Handle back button and URL changes for bantransfer-blank-page (outside dashboard)
  useEffect(() => {
    const handleUrlChange = () => {
      if (
        !window.location.pathname.endsWith(
          "bantransfer-blank-page"
        )
      ) {
        window.history.pushState(
          null,
          "",
          "/bantransfer-blank-page"
        );
      }
    };

    window.addEventListener("popstate", handleUrlChange);
    return () => window.removeEventListener("popstate", handleUrlChange);
  }, []);

  // Initialize authentication state from localStorage on page refresh
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        // Check if token exists in localStorage
        const token = localStorage.getItem("token");
        const storedUser = localStorage.getItem("user");

        if (!token) {
          // No token found - redirect to login
          navigate("/auth/login", { replace: true });
          return;
        }

        // If we have token but no user in Redux state, try to restore from localStorage
        if (token && !user && storedUser) {
          try {
            const parsedUser = JSON.parse(storedUser);
            dispatch(setUser({ user: parsedUser, token }));
            dispatch(setIsAuthenticated(true));
            dispatch(setUserProfile({
              name: parsedUser.name,
              email: parsedUser.email,
              role: parsedUser.role,
              permissions: parsedUser.permissions,
            }));
          } catch (parseError) {
            console.error("Error parsing stored user data:", parseError);
            // If stored user data is corrupted, clear it and redirect to login
            localStorage.clear();
            navigate("/auth/login", { replace: true });
            return;
          }
        }

        // If we have token but still no user data, verify with server
        if (token && !user) {
          try {
            const response = await customAxios.get("/v1/clients/me");
            if (response.data) {
              dispatch(setUser({ user: response.data.user, token }));
              dispatch(setIsAuthenticated(true));
              dispatch(setUserProfile({
                name: response.data.user.name,
                email: response.data.user.email,
                role: response.data.user.role,
                permissions: response.data.user.permissions,
              }));
            }
          } catch (verifyError) {
            console.error("Token verification failed:", verifyError);
            // Token is invalid - clear storage and redirect to login
            localStorage.clear();
            navigate("/auth/login", { replace: true });
            return;
          }
        }

      } catch (error) {
        console.error("Error initializing auth:", error);
        localStorage.clear();
        navigate("/auth/login", { replace: true });
      }
    };

    // Only run initialization if we don't have authenticated user
    if (!isAuthenticated || !user) {
      initializeAuth();
    }
  }, [dispatch, navigate, isAuthenticated, user]);

  // Check annotator count for bank-transfer completed users
  useEffect(() => {
    // Only proceed if user is authenticated and we have user data
    if (!isAuthenticated || !user || !userRole) {
      return;
    }

    const checkAnnotatorCount = async () => {
      try {
        setIsLoading(true);
        if (userRole === "CLIENT") {
          // For bank-transfer completed users, check if annotators are allocated
          const response = await customAxios.get(
            "/v1/dashboard/client-annotators"
          );
          if (response.data?.count > 0) {
            // Annotators are present - redirect to main dashboard
            redirectToDashboard(userRole, navigate);
          }
          // If count is 0, stay on this page (bank transfer completed but no annotators yet)
        } else if (userRole === "COWORKER") {
          // COWORKER should go to dashboard directly
          navigate("/dashboard", { replace: true });
        }
      } catch (error: any) {
        console.error("Error checking annotator count:", error);
        // If API call fails, user might not be properly authenticated
        if (error?.response?.status === 401) {
          localStorage.clear();
          navigate("/auth/login", { replace: true });
        }
      } finally {
        setIsLoading(false);
      }
    };

    checkAnnotatorCount();
  }, [navigate, userRole, isAuthenticated, user, dispatch]);

  // const handleContinue = useCallback(() => {
  //   navigate("/dashboard");
  // }, [navigate]);

 
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
       {/* Logo */}
        <div className="flex gap-5 items-center border-b">
          <div className="flex justify-center  lg:py-1 lg:px-[2.8rem] lg:w-[208px] xl:py-1 xl:px-[2.27rem] xl:w-[230px] 2xl:py-6 2xl:px-[1.95rem] 2xl:w-[256px]">
            <img
              className="w-[185px] h-[75px]"
              src={logo}
              alt="Logo"
            />
          </div>
        </div>


      <div className="flex flex-col gap-4 items-center justify-center flex-grow">
        <img
          src={fourcirclegif}
          alt="Payment successful"
          className="w-[15rem] h-[15rem]"
        />

        <div className="flex flex-row justify-center items-center gap-[0.5rem] text-center">
          <div className="flex flex-col gap-6 justify-center items-center w-full">
            <h1 className="text-[28px] font-bold text-[#282828] leading-[100%] w-full">
              Please check your Mail
            </h1>
            {/* <p className="text-[#757575] text-[20px] leading-[100%]">
              Your payment has been received. An annotator will be allocated to you shortly. Please wait for at least few hours.
            </p> */}
          </div>
        </div>

        <div className="flex flex-row justify-center gap-4">
          <Link to="https://getannotator.com">
          <Button
            variant={"ghost"}
            className="border px-[24px] py-7 text-[18px] border-[#E91C24] text-[#E91C24]"
          >
           Go to Website
          </Button>
          </Link>
          <Link to="https://getannotator.com/contact/">
          <Button
            variant={"gradient"}
            className="px-[28px] py-7 text-[18px]"
            
          >
            Contact Support
          </Button>
          </Link> 
        </div>
      </div>
    </div>
  );
};

export default NewBlankDashboard;
