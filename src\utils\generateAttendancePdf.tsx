import { createRoot } from 'react-dom/client';
import { AttendanceTemplate } from '@/components/attendance/AttendanceTemplate';

interface AttendanceRecord {
  date: string;
  checkIn: string;
  checkOut: string;
  totalHours: string;
  status: 'Present' | 'Absent' | 'Late' | 'Half Day';
  project?: string;
}

interface AttendanceData {
  employeeName: string;
  employeeId: string;
  department: string;
  month: string;
  year: string;
  records: AttendanceRecord[];
  totalWorkingDays: number;
  totalPresent: number;
  totalAbsent: number;
  totalLate: number;
  totalHours: string;
}

// Main PDF generation function using html2canvas
export const generateAttendancePdf = async (attendanceData: AttendanceData) => {
  const html2canvas = (await import('html2canvas')).default;
  const jsPDF = (await import('jspdf')).jsPDF;

  // Create a temporary container
  const container = document.createElement('div');
  container.style.position = 'absolute';
  container.style.left = '-9999px';
  container.style.top = '-9999px';
  container.style.width = '210mm';
  container.style.height = 'auto';
  container.style.backgroundColor = '#ffffff';
  document.body.appendChild(container);

  // Create root and render the component
  const root = createRoot(container);
  
  return new Promise<void>((resolve, reject) => {
    try {
      root.render(
        <AttendanceTemplate attendanceData={attendanceData} />
      );

      // Wait for render to complete
      setTimeout(async () => {
        try {
          const canvas = await html2canvas(container, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: 794, // A4 width in pixels at 96 DPI
            height: 1123, // A4 height in pixels at 96 DPI
            scrollX: 0,
            scrollY: 0,
            windowWidth: 794,
            windowHeight: 1123
          });

          const imgData = canvas.toDataURL('image/png');
          const pdf = new jsPDF('p', 'mm', 'a4');
          
          const imgWidth = 210;
          const pageHeight = 295;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          let heightLeft = imgHeight;
          let position = 0;

          // Add first page
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;

          // Add additional pages if needed
          while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
          }

          // Save the PDF
          pdf.save(`ATTENDANCE_REPORT_${attendanceData.employeeId}_${attendanceData.month}_${attendanceData.year}.pdf`);
          
          // Cleanup
          root.unmount();
          document.body.removeChild(container);
          resolve();
        } catch (error) {
          console.error('PDF generation error:', error);
          root.unmount();
          document.body.removeChild(container);
          reject(error);
        }
      }, 1000); // Increased timeout for complex rendering
    } catch (error) {
      console.error('Render error:', error);
      root.unmount();
      document.body.removeChild(container);
      reject(error);
    }
  });
};

// Sample data generator for testing
export const generateSampleAttendanceData = (): AttendanceData => {
  const currentDate = new Date();
  const currentMonth = currentDate.toLocaleString('default', { month: 'long' });
  const currentYear = currentDate.getFullYear().toString();
  
  // Generate sample records for the month
  const records: AttendanceRecord[] = [];
  const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();
  
  for (let day = 1; day <= Math.min(daysInMonth, 20); day++) {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    const dayOfWeek = date.getDay();
    
    // Skip weekends
    if (dayOfWeek === 0 || dayOfWeek === 6) continue;
    
    const statuses: ('Present' | 'Absent' | 'Late' | 'Half Day')[] = ['Present', 'Present', 'Present', 'Late', 'Absent'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    
    let checkIn = '';
    let checkOut = '';
    let totalHours = '';
    
    if (randomStatus !== 'Absent') {
      const baseCheckIn = randomStatus === 'Late' ? 10 : 9;
      const checkInMinutes = Math.floor(Math.random() * 30);
      checkIn = `${baseCheckIn}:${checkInMinutes.toString().padStart(2, '0')} AM`;
      
      const checkOutHour = randomStatus === 'Half Day' ? 13 : 18;
      const checkOutMinutes = Math.floor(Math.random() * 30);
      checkOut = `${checkOutHour}:${checkOutMinutes.toString().padStart(2, '0')} PM`;
      
      const hours = randomStatus === 'Half Day' ? 4 : 8;
      totalHours = `${hours}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`;
    }
    
    records.push({
      date: date.toISOString().split('T')[0],
      checkIn,
      checkOut,
      totalHours,
      status: randomStatus,
      project: randomStatus !== 'Absent' ? 'Data Annotation Project' : undefined
    });
  }
  
  const totalPresent = records.filter(r => r.status === 'Present').length;
  const totalAbsent = records.filter(r => r.status === 'Absent').length;
  const totalLate = records.filter(r => r.status === 'Late').length;
  const totalWorkingHours = records.reduce((sum, record) => {
    if (record.totalHours) {
      const [hours, minutes] = record.totalHours.split(':').map(Number);
      return sum + hours + (minutes / 60);
    }
    return sum;
  }, 0);
  
  return {
    employeeName: 'John Doe',
    employeeId: 'EMP001',
    department: 'Data Annotation',
    month: currentMonth,
    year: currentYear,
    records,
    totalWorkingDays: records.length,
    totalPresent,
    totalAbsent,
    totalLate,
    totalHours: `${Math.floor(totalWorkingHours)}:${Math.floor((totalWorkingHours % 1) * 60).toString().padStart(2, '0')}`
  };
};
